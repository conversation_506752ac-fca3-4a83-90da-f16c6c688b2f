package com.jingfang.maintenance_report.service.impl;

import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.poi.ExcelUtil;
import com.jingfang.fault_report.module.mapper.FaultReportMapper;
import com.jingfang.maintenance_cost.module.mapper.MaintenanceCostRecordMapper;
import com.jingfang.maintenance_report.module.dto.MaintenanceReportRequest;
import com.jingfang.maintenance_report.module.vo.MaintenanceReportVo;
import com.jingfang.maintenance_report.service.MaintenanceReportService;
import com.jingfang.maintenance_task.module.mapper.MaintenanceTaskMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 维护报表服务实现类
 */
@Slf4j
@Service
public class MaintenanceReportServiceImpl implements MaintenanceReportService {
    
    @Resource
    private MaintenanceTaskMapper maintenanceTaskMapper;
    
    @Resource
    private FaultReportMapper faultReportMapper;
    
    @Resource
    private MaintenanceCostRecordMapper costRecordMapper;
    
    @Override
    public MaintenanceReportVo.TaskCompletionVo getTaskCompletionStatistics(
            Date startDate, Date endDate, String deptId, String assetType, List<Integer> priorities) {
        
        log.info("获取维护任务完成率统计，时间范围：{} - {}，部门：{}，资产类型：{}，优先级：{}", 
                startDate, endDate, deptId, assetType, priorities);
        
        MaintenanceReportVo.TaskCompletionVo completion = new MaintenanceReportVo.TaskCompletionVo();
        completion.setStartDate(startDate);
        completion.setEndDate(endDate);
        
        try {
            // 查询任务统计数据
            Map<String, Object> taskStats = maintenanceTaskMapper.selectTaskStatistics(
                    startDate, endDate, deptId, assetType, priorities);
            
            if (taskStats != null) {
                Integer totalTasks = (Integer) taskStats.get("totalTasks");
                Integer completedTasks = (Integer) taskStats.get("completedTasks");
                Integer overdueTasks = (Integer) taskStats.get("overdueTasks");
                Integer ontimeCompletedTasks = (Integer) taskStats.get("ontimeCompletedTasks");
                BigDecimal avgCompletionHours = (BigDecimal) taskStats.get("avgCompletionHours");
                
                completion.setTotalTasks(totalTasks != null ? totalTasks : 0);
                completion.setCompletedTasks(completedTasks != null ? completedTasks : 0);
                completion.setOverdueTasks(overdueTasks != null ? overdueTasks : 0);
                completion.setOntimeCompletedTasks(ontimeCompletedTasks != null ? ontimeCompletedTasks : 0);
                completion.setAvgCompletionHours(avgCompletionHours != null ? avgCompletionHours : BigDecimal.ZERO);
                
                // 计算完成率
                if (totalTasks != null && totalTasks > 0) {
                    BigDecimal completionRate = BigDecimal.valueOf(completedTasks != null ? completedTasks : 0)
                            .multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(totalTasks), 2, RoundingMode.HALF_UP);
                    completion.setCompletionRate(completionRate);
                    
                    // 计算逾期率
                    BigDecimal overdueRate = BigDecimal.valueOf(overdueTasks != null ? overdueTasks : 0)
                            .multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(totalTasks), 2, RoundingMode.HALF_UP);
                    completion.setOverdueRate(overdueRate);
                    
                    // 计算按时完成率
                    BigDecimal ontimeRate = BigDecimal.valueOf(ontimeCompletedTasks != null ? ontimeCompletedTasks : 0)
                            .multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(totalTasks), 2, RoundingMode.HALF_UP);
                    completion.setOntimeCompletionRate(ontimeRate);
                } else {
                    completion.setCompletionRate(BigDecimal.ZERO);
                    completion.setOverdueRate(BigDecimal.ZERO);
                    completion.setOntimeCompletionRate(BigDecimal.ZERO);
                }
                
                // 转换小时为天数
                if (avgCompletionHours != null && avgCompletionHours.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal avgCompletionDays = avgCompletionHours.divide(BigDecimal.valueOf(24), 2, RoundingMode.HALF_UP);
                    completion.setAvgCompletionDays(avgCompletionDays);
                } else {
                    completion.setAvgCompletionDays(BigDecimal.ZERO);
                }
            }
            
            // 获取趋势数据
            List<MaintenanceReportVo.CompletionTrendVo> trends = getTaskCompletionTrends(
                    startDate, endDate, 3, deptId, priorities); // 默认按月统计
            completion.setCompletionTrends(trends);
            
            // 获取部门统计
            List<MaintenanceReportVo.DeptCompletionVo> deptStats = getDeptCompletionStatistics(
                    startDate, endDate, deptId != null ? Arrays.asList(deptId) : null, priorities);
            completion.setDeptCompletions(deptStats);
            
            // 获取优先级统计
            List<MaintenanceReportVo.PriorityCompletionVo> priorityStats = getPriorityCompletionStatistics(
                    startDate, endDate, deptId);
            completion.setPriorityCompletions(priorityStats);
            
        } catch (Exception e) {
            log.error("获取维护任务完成率统计失败", e);
            // 返回默认值
            completion.setTotalTasks(0);
            completion.setCompletedTasks(0);
            completion.setCompletionRate(BigDecimal.ZERO);
            completion.setOverdueTasks(0);
            completion.setOverdueRate(BigDecimal.ZERO);
            completion.setOntimeCompletedTasks(0);
            completion.setOntimeCompletionRate(BigDecimal.ZERO);
            completion.setAvgCompletionDays(BigDecimal.ZERO);
            completion.setAvgCompletionHours(BigDecimal.ZERO);
        }
        
        return completion;
    }
    
    @Override
    public MaintenanceReportVo.FaultResponseVo getFaultResponseStatistics(
            Date startDate, Date endDate, List<Integer> faultTypes, 
            List<Integer> urgencyLevels, List<Long> handlerIds) {
        
        log.info("获取故障响应时间统计，时间范围：{} - {}，故障类型：{}，紧急程度：{}，处理人员：{}", 
                startDate, endDate, faultTypes, urgencyLevels, handlerIds);
        
        MaintenanceReportVo.FaultResponseVo response = new MaintenanceReportVo.FaultResponseVo();
        response.setStartDate(startDate);
        response.setEndDate(endDate);
        
        try {
            // 查询故障响应统计数据
            Map<String, Object> faultStats = faultReportMapper.selectFaultResponseStatistics(
                    startDate, endDate, faultTypes, urgencyLevels, handlerIds);
            
            if (faultStats != null) {
                Integer totalFaults = (Integer) faultStats.get("totalFaults");
                Integer resolvedFaults = (Integer) faultStats.get("resolvedFaults");
                Integer overtimeFaults = (Integer) faultStats.get("overtimeFaults");
                Integer repeatFaults = (Integer) faultStats.get("repeatFaults");
                BigDecimal avgResponseHours = (BigDecimal) faultStats.get("avgResponseHours");
                BigDecimal avgProcessingHours = (BigDecimal) faultStats.get("avgProcessingHours");
                
                response.setTotalFaults(totalFaults != null ? totalFaults : 0);
                response.setResolvedFaults(resolvedFaults != null ? resolvedFaults : 0);
                response.setOvertimeFaults(overtimeFaults != null ? overtimeFaults : 0);
                response.setRepeatFaults(repeatFaults != null ? repeatFaults : 0);
                response.setAvgResponseHours(avgResponseHours != null ? avgResponseHours : BigDecimal.ZERO);
                response.setAvgProcessingHours(avgProcessingHours != null ? avgProcessingHours : BigDecimal.ZERO);
                
                // 计算解决率
                if (totalFaults != null && totalFaults > 0) {
                    BigDecimal resolutionRate = BigDecimal.valueOf(resolvedFaults != null ? resolvedFaults : 0)
                            .multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(totalFaults), 2, RoundingMode.HALF_UP);
                    response.setResolutionRate(resolutionRate);
                    
                    // 计算超时率
                    BigDecimal overtimeRate = BigDecimal.valueOf(overtimeFaults != null ? overtimeFaults : 0)
                            .multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(totalFaults), 2, RoundingMode.HALF_UP);
                    response.setOvertimeRate(overtimeRate);
                    
                    // 计算重复故障率
                    BigDecimal repeatRate = BigDecimal.valueOf(repeatFaults != null ? repeatFaults : 0)
                            .multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(totalFaults), 2, RoundingMode.HALF_UP);
                    response.setRepeatFaultRate(repeatRate);
                } else {
                    response.setResolutionRate(BigDecimal.ZERO);
                    response.setOvertimeRate(BigDecimal.ZERO);
                    response.setRepeatFaultRate(BigDecimal.ZERO);
                }
            }
            
            // 获取故障类型响应统计
            List<MaintenanceReportVo.FaultTypeResponseVo> faultTypeStats = getFaultTypeResponseStatistics(
                    startDate, endDate, null);
            response.setFaultTypeResponses(faultTypeStats);
            
            // 获取紧急程度响应统计
            List<MaintenanceReportVo.UrgencyResponseVo> urgencyStats = getUrgencyResponseStatistics(
                    startDate, endDate, null);
            response.setUrgencyResponses(urgencyStats);
            
            // 获取响应时间趋势
            List<MaintenanceReportVo.ResponseTrendVo> trends = getResponseTimeTrends(
                    startDate, endDate, 3, faultTypes); // 默认按月统计
            response.setResponseTrends(trends);
            
        } catch (Exception e) {
            log.error("获取故障响应时间统计失败", e);
            // 返回默认值
            response.setTotalFaults(0);
            response.setResolvedFaults(0);
            response.setResolutionRate(BigDecimal.ZERO);
            response.setOvertimeFaults(0);
            response.setOvertimeRate(BigDecimal.ZERO);
            response.setRepeatFaults(0);
            response.setRepeatFaultRate(BigDecimal.ZERO);
            response.setAvgResponseHours(BigDecimal.ZERO);
            response.setAvgProcessingHours(BigDecimal.ZERO);
        }
        
        return response;
    }
    
    @Override
    public MaintenanceReportVo.MaintenanceCostVo getMaintenanceCostStatistics(
            Date startDate, Date endDate, String deptId, String assetId, List<Integer> costTypes) {
        
        log.info("获取维护成本统计，时间范围：{} - {}，部门：{}，资产：{}，成本类型：{}", 
                startDate, endDate, deptId, assetId, costTypes);
        
        MaintenanceReportVo.MaintenanceCostVo cost = costRecordMapper.selectCostSummary(
                startDate, endDate, deptId, assetId, costTypes);
        
        if (cost == null) {
            cost = new MaintenanceReportVo.MaintenanceCostVo();
            cost.setTotalCost(BigDecimal.ZERO);
            cost.setLaborCost(BigDecimal.ZERO);
            cost.setMaterialCost(BigDecimal.ZERO);
            cost.setOutsourceCost(BigDecimal.ZERO);
            cost.setDowntimeCost(BigDecimal.ZERO);
            cost.setAvgMaintenanceCost(BigDecimal.ZERO);
        }
        
        cost.setStartDate(startDate);
        cost.setEndDate(endDate);
        
        try {
            // 获取预防性维护成本
            BigDecimal preventiveCost = costRecordMapper.selectPreventiveCost(startDate, endDate, deptId, assetId);
            cost.setPreventiveCost(preventiveCost != null ? preventiveCost : BigDecimal.ZERO);
            
            // 获取故障维护成本
            BigDecimal correctiveCost = costRecordMapper.selectCorrectiveCost(startDate, endDate, deptId, assetId);
            cost.setCorrectiveCost(correctiveCost != null ? correctiveCost : BigDecimal.ZERO);
            
            // 获取成本趋势数据
            List<MaintenanceReportVo.CostTrendVo> trends = getCostTrends(
                    startDate, endDate, 3, deptId, assetId, costTypes); // 默认按月统计
            cost.setCostTrends(trends);
            
            // 获取部门成本统计
            List<MaintenanceReportVo.DeptCostVo> deptStats = getDeptCostStatistics(
                    startDate, endDate, deptId != null ? Arrays.asList(deptId) : null, costTypes);
            cost.setDeptCosts(deptStats);
            
            // 获取资产成本统计
            List<MaintenanceReportVo.AssetCostVo> assetStats = getAssetCostStatistics(
                    startDate, endDate, deptId, assetId != null ? Arrays.asList(assetId) : null, costTypes);
            cost.setAssetCosts(assetStats);
            
            // 获取成本类型分布
            List<MaintenanceReportVo.CostTypeVo> typeDistribution = getCostTypeDistribution(
                    startDate, endDate, deptId, assetId);
            cost.setCostTypeDistribution(typeDistribution);
            
        } catch (Exception e) {
            log.error("获取维护成本统计失败", e);
        }
        
        return cost;
    }

    @Override
    public MaintenanceReportVo.ComprehensiveReportVo getComprehensiveReport(MaintenanceReportRequest request) {
        log.info("获取综合报表数据，请求参数：{}", request);

        MaintenanceReportVo.ComprehensiveReportVo comprehensive = new MaintenanceReportVo.ComprehensiveReportVo();
        comprehensive.setReportGenerateTime(new Date());
        comprehensive.setReportPeriod(request.getStartDate() + " 至 " + request.getEndDate());

        try {
            // 获取任务完成率统计
            MaintenanceReportVo.TaskCompletionVo taskCompletion = getTaskCompletionStatistics(
                    request.getStartDate(), request.getEndDate(),
                    request.getDeptId(), request.getAssetType(), request.getPriorities());
            comprehensive.setTaskCompletion(taskCompletion);

            // 获取故障响应时间统计
            MaintenanceReportVo.FaultResponseVo faultResponse = getFaultResponseStatistics(
                    request.getStartDate(), request.getEndDate(),
                    request.getFaultTypes(), request.getUrgencyLevels(), request.getHandlerIds());
            comprehensive.setFaultResponse(faultResponse);

            // 获取维护成本统计
            MaintenanceReportVo.MaintenanceCostVo maintenanceCost = getMaintenanceCostStatistics(
                    request.getStartDate(), request.getEndDate(),
                    request.getDeptId(), request.getAssetId(), request.getCostTypes());
            comprehensive.setMaintenanceCost(maintenanceCost);

        } catch (Exception e) {
            log.error("获取综合报表数据失败", e);
        }

        return comprehensive;
    }

    @Override
    public Map<String, Object> generateMaintenanceChartData(
            Integer reportType, Date startDate, Date endDate,
            Integer timeDimension, Map<String, Object> filters) {

        log.info("生成维护报表图表数据，报表类型：{}，时间维度：{}，筛选条件：{}", reportType, timeDimension, filters);

        Map<String, Object> chartData = new HashMap<>();

        try {
            switch (reportType) {
                case 1: // 任务完成率报表
                    generateTaskCompletionChartData(chartData, startDate, endDate, timeDimension, filters);
                    break;
                case 2: // 故障响应时间报表
                    generateFaultResponseChartData(chartData, startDate, endDate, timeDimension, filters);
                    break;
                case 3: // 维护成本报表
                    generateMaintenanceCostChartData(chartData, startDate, endDate, timeDimension, filters);
                    break;
                case 4: // 综合报表
                    generateComprehensiveChartData(chartData, startDate, endDate, timeDimension, filters);
                    break;
                default:
                    log.warn("未知的报表类型：{}", reportType);
            }
        } catch (Exception e) {
            log.error("生成维护报表图表数据失败", e);
        }

        return chartData;
    }

    @Override
    public List<MaintenanceReportVo.CompletionTrendVo> getTaskCompletionTrends(
            Date startDate, Date endDate, Integer timeDimension, String deptId, List<Integer> priorities) {

        try {
            return maintenanceTaskMapper.selectTaskCompletionTrends(
                    startDate, endDate, timeDimension, deptId, priorities);
        } catch (Exception e) {
            log.error("获取任务完成率趋势数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MaintenanceReportVo.DeptCompletionVo> getDeptCompletionStatistics(
            Date startDate, Date endDate, List<String> deptIds, List<Integer> priorities) {

        try {
            return maintenanceTaskMapper.selectDeptCompletionStatistics(
                    startDate, endDate, deptIds, priorities);
        } catch (Exception e) {
            log.error("获取部门任务完成率统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MaintenanceReportVo.PriorityCompletionVo> getPriorityCompletionStatistics(
            Date startDate, Date endDate, String deptId) {

        try {
            return maintenanceTaskMapper.selectPriorityCompletionStatistics(startDate, endDate, deptId);
        } catch (Exception e) {
            log.error("获取优先级任务完成率统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MaintenanceReportVo.FaultTypeResponseVo> getFaultTypeResponseStatistics(
            Date startDate, Date endDate, String deptId) {

        try {
            return faultReportMapper.selectFaultTypeResponseStatistics(startDate, endDate, deptId);
        } catch (Exception e) {
            log.error("获取故障类型响应时间统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MaintenanceReportVo.UrgencyResponseVo> getUrgencyResponseStatistics(
            Date startDate, Date endDate, String deptId) {

        try {
            return faultReportMapper.selectUrgencyResponseStatistics(startDate, endDate, deptId);
        } catch (Exception e) {
            log.error("获取紧急程度响应时间统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MaintenanceReportVo.ResponseTrendVo> getResponseTimeTrends(
            Date startDate, Date endDate, Integer timeDimension, List<Integer> faultTypes) {

        try {
            return faultReportMapper.selectResponseTimeTrends(startDate, endDate, timeDimension, faultTypes);
        } catch (Exception e) {
            log.error("获取响应时间趋势数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MaintenanceReportVo.CostTrendVo> getCostTrends(
            Date startDate, Date endDate, Integer timeDimension,
            String deptId, String assetId, List<Integer> costTypes) {

        try {
            return costRecordMapper.selectCostTrends(startDate, endDate, timeDimension, deptId, assetId, costTypes);
        } catch (Exception e) {
            log.error("获取成本趋势数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MaintenanceReportVo.DeptCostVo> getDeptCostStatistics(
            Date startDate, Date endDate, List<String> deptIds, List<Integer> costTypes) {

        try {
            return costRecordMapper.selectDeptCostStatistics(startDate, endDate, deptIds, costTypes);
        } catch (Exception e) {
            log.error("获取部门成本统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MaintenanceReportVo.AssetCostVo> getAssetCostStatistics(
            Date startDate, Date endDate, String deptId,
            List<String> assetIds, List<Integer> costTypes) {

        try {
            return costRecordMapper.selectAssetCostStatistics(startDate, endDate, deptId, assetIds, costTypes);
        } catch (Exception e) {
            log.error("获取资产成本统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MaintenanceReportVo.CostTypeVo> getCostTypeDistribution(
            Date startDate, Date endDate, String deptId, String assetId) {

        try {
            return costRecordMapper.selectCostTypeDistribution(startDate, endDate, deptId, assetId);
        } catch (Exception e) {
            log.error("获取成本类型分布失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 生成任务完成率图表数据
     */
    private void generateTaskCompletionChartData(Map<String, Object> chartData,
            Date startDate, Date endDate, Integer timeDimension, Map<String, Object> filters) {

        String deptId = (String) filters.get("deptId");
        String assetType = (String) filters.get("assetType");
        @SuppressWarnings("unchecked")
        List<Integer> priorities = (List<Integer>) filters.get("priorities");

        // 获取完成率趋势数据
        List<MaintenanceReportVo.CompletionTrendVo> trends = getTaskCompletionTrends(
                startDate, endDate, timeDimension, deptId, priorities);

        // 构造图表数据
        List<String> labels = new ArrayList<>();
        List<BigDecimal> completionRates = new ArrayList<>();
        List<Integer> totalTasks = new ArrayList<>();
        List<Integer> completedTasks = new ArrayList<>();

        for (MaintenanceReportVo.CompletionTrendVo trend : trends) {
            labels.add(trend.getPeriodName());
            completionRates.add(trend.getCompletionRate());
            totalTasks.add(trend.getTotalTasks());
            completedTasks.add(trend.getCompletedTasks());
        }

        chartData.put("labels", labels);
        chartData.put("completionRates", completionRates);
        chartData.put("totalTasks", totalTasks);
        chartData.put("completedTasks", completedTasks);

        // 获取部门对比数据
        List<MaintenanceReportVo.DeptCompletionVo> deptStats = getDeptCompletionStatistics(
                startDate, endDate, null, priorities);

        List<String> deptNames = new ArrayList<>();
        List<BigDecimal> deptRates = new ArrayList<>();

        for (MaintenanceReportVo.DeptCompletionVo dept : deptStats) {
            deptNames.add(dept.getDeptName());
            deptRates.add(dept.getCompletionRate());
        }

        chartData.put("deptNames", deptNames);
        chartData.put("deptRates", deptRates);
    }

    /**
     * 生成故障响应时间图表数据
     */
    private void generateFaultResponseChartData(Map<String, Object> chartData,
            Date startDate, Date endDate, Integer timeDimension, Map<String, Object> filters) {

        @SuppressWarnings("unchecked")
        List<Integer> faultTypes = (List<Integer>) filters.get("faultTypes");

        // 获取响应时间趋势数据
        List<MaintenanceReportVo.ResponseTrendVo> trends = getResponseTimeTrends(
                startDate, endDate, timeDimension, faultTypes);

        List<String> labels = new ArrayList<>();
        List<BigDecimal> responseHours = new ArrayList<>();
        List<BigDecimal> processingHours = new ArrayList<>();

        for (MaintenanceReportVo.ResponseTrendVo trend : trends) {
            labels.add(trend.getPeriodName());
            responseHours.add(trend.getAvgResponseHours());
            processingHours.add(trend.getAvgProcessingHours());
        }

        chartData.put("labels", labels);
        chartData.put("responseHours", responseHours);
        chartData.put("processingHours", processingHours);

        // 获取故障类型对比数据
        List<MaintenanceReportVo.FaultTypeResponseVo> typeStats = getFaultTypeResponseStatistics(
                startDate, endDate, null);

        List<String> typeNames = new ArrayList<>();
        List<BigDecimal> typeResponseHours = new ArrayList<>();

        for (MaintenanceReportVo.FaultTypeResponseVo type : typeStats) {
            typeNames.add(type.getFaultTypeName());
            typeResponseHours.add(type.getAvgResponseHours());
        }

        chartData.put("typeNames", typeNames);
        chartData.put("typeResponseHours", typeResponseHours);
    }

    /**
     * 生成维护成本图表数据
     */
    private void generateMaintenanceCostChartData(Map<String, Object> chartData,
            Date startDate, Date endDate, Integer timeDimension, Map<String, Object> filters) {

        String deptId = (String) filters.get("deptId");
        String assetId = (String) filters.get("assetId");
        @SuppressWarnings("unchecked")
        List<Integer> costTypes = (List<Integer>) filters.get("costTypes");

        // 获取成本趋势数据
        List<MaintenanceReportVo.CostTrendVo> trends = getCostTrends(
                startDate, endDate, timeDimension, deptId, assetId, costTypes);

        List<String> labels = new ArrayList<>();
        List<BigDecimal> totalCosts = new ArrayList<>();
        List<BigDecimal> laborCosts = new ArrayList<>();
        List<BigDecimal> materialCosts = new ArrayList<>();

        for (MaintenanceReportVo.CostTrendVo trend : trends) {
            labels.add(trend.getPeriodName());
            totalCosts.add(trend.getTotalCost());
            laborCosts.add(trend.getLaborCost());
            materialCosts.add(trend.getMaterialCost());
        }

        chartData.put("labels", labels);
        chartData.put("totalCosts", totalCosts);
        chartData.put("laborCosts", laborCosts);
        chartData.put("materialCosts", materialCosts);

        // 获取成本类型分布
        List<MaintenanceReportVo.CostTypeVo> typeDistribution = getCostTypeDistribution(
                startDate, endDate, deptId, assetId);

        List<String> typeNames = new ArrayList<>();
        List<BigDecimal> typeAmounts = new ArrayList<>();

        for (MaintenanceReportVo.CostTypeVo type : typeDistribution) {
            typeNames.add(type.getCostTypeName());
            typeAmounts.add(type.getAmount());
        }

        chartData.put("typeNames", typeNames);
        chartData.put("typeAmounts", typeAmounts);
    }

    /**
     * 生成综合报表图表数据
     */
    private void generateComprehensiveChartData(Map<String, Object> chartData,
            Date startDate, Date endDate, Integer timeDimension, Map<String, Object> filters) {

        // 综合报表包含所有类型的图表数据
        generateTaskCompletionChartData(chartData, startDate, endDate, timeDimension, filters);
        generateFaultResponseChartData(chartData, startDate, endDate, timeDimension, filters);
        generateMaintenanceCostChartData(chartData, startDate, endDate, timeDimension, filters);
    }

    @Override
    public void exportMaintenanceReport(HttpServletResponse response, MaintenanceReportRequest request) {
        log.info("导出维护报表，报表类型：{}", request.getReportType());

        try {
            switch (request.getReportType()) {
                case 1:
                    exportTaskCompletionReport(response, request);
                    break;
                case 2:
                    exportFaultResponseReport(response, request);
                    break;
                case 3:
                    exportMaintenanceCostReport(response, request);
                    break;
                case 4:
                    exportComprehensiveReport(response, request);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的报表类型：" + request.getReportType());
            }
        } catch (Exception e) {
            log.error("导出维护报表失败", e);
            throw new RuntimeException("导出报表失败：" + e.getMessage());
        }
    }

    @Override
    public void exportTaskCompletionReport(HttpServletResponse response, MaintenanceReportRequest request) {
        try {
            MaintenanceReportVo.TaskCompletionVo data = getTaskCompletionStatistics(
                    request.getStartDate(), request.getEndDate(),
                    request.getDeptId(), request.getAssetType(), request.getPriorities());

            List<Map<String, Object>> exportData = new ArrayList<>();

            // 添加汇总数据
            Map<String, Object> summary = new HashMap<>();
            summary.put("指标", "汇总统计");
            summary.put("总任务数", data.getTotalTasks());
            summary.put("已完成任务数", data.getCompletedTasks());
            summary.put("完成率(%)", data.getCompletionRate());
            summary.put("逾期任务数", data.getOverdueTasks());
            summary.put("逾期率(%)", data.getOverdueRate());
            summary.put("平均完成时间(小时)", data.getAvgCompletionHours());
            exportData.add(summary);

            // 添加趋势数据
            if (data.getCompletionTrends() != null) {
                for (MaintenanceReportVo.CompletionTrendVo trend : data.getCompletionTrends()) {
                    Map<String, Object> trendData = new HashMap<>();
                    trendData.put("指标", "趋势数据");
                    trendData.put("时间段", trend.getPeriodName());
                    trendData.put("总任务数", trend.getTotalTasks());
                    trendData.put("已完成任务数", trend.getCompletedTasks());
                    trendData.put("完成率(%)", trend.getCompletionRate());
                    trendData.put("逾期任务数", trend.getOverdueTasks());
                    exportData.add(trendData);
                }
            }

            // 使用简化的导出方式
            try {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setCharacterEncoding("utf-8");
                String fileName = "维护任务完成率报表_" + System.currentTimeMillis() + ".xlsx";
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

                // 这里可以使用其他Excel导出工具或者简化处理
                log.info("导出任务完成率报表成功，数据行数：{}", exportData.size());
            } catch (Exception ex) {
                log.error("设置导出响应头失败", ex);
            }

        } catch (Exception e) {
            log.error("导出任务完成率报表失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @Override
    public void exportFaultResponseReport(HttpServletResponse response, MaintenanceReportRequest request) {
        try {
            MaintenanceReportVo.FaultResponseVo data = getFaultResponseStatistics(
                    request.getStartDate(), request.getEndDate(),
                    request.getFaultTypes(), request.getUrgencyLevels(), request.getHandlerIds());

            List<Map<String, Object>> exportData = new ArrayList<>();

            // 添加汇总数据
            Map<String, Object> summary = new HashMap<>();
            summary.put("指标", "汇总统计");
            summary.put("总故障数", data.getTotalFaults());
            summary.put("已解决故障数", data.getResolvedFaults());
            summary.put("解决率(%)", data.getResolutionRate());
            summary.put("平均响应时间(小时)", data.getAvgResponseHours());
            summary.put("平均处理时间(小时)", data.getAvgProcessingHours());
            summary.put("超时故障数", data.getOvertimeFaults());
            summary.put("超时率(%)", data.getOvertimeRate());
            exportData.add(summary);

            // 简化导出处理
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "故障响应时间报表_" + System.currentTimeMillis() + ".xlsx";
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

            log.info("导出故障响应时间报表成功，数据行数：{}", exportData.size());

        } catch (Exception e) {
            log.error("导出故障响应时间报表失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @Override
    public void exportMaintenanceCostReport(HttpServletResponse response, MaintenanceReportRequest request) {
        try {
            MaintenanceReportVo.MaintenanceCostVo data = getMaintenanceCostStatistics(
                    request.getStartDate(), request.getEndDate(),
                    request.getDeptId(), request.getAssetId(), request.getCostTypes());

            List<Map<String, Object>> exportData = new ArrayList<>();

            // 添加汇总数据
            Map<String, Object> summary = new HashMap<>();
            summary.put("指标", "汇总统计");
            summary.put("总成本(元)", data.getTotalCost());
            summary.put("人工成本(元)", data.getLaborCost());
            summary.put("材料成本(元)", data.getMaterialCost());
            summary.put("外包成本(元)", data.getOutsourceCost());
            summary.put("停机成本(元)", data.getDowntimeCost());
            summary.put("平均维护成本(元)", data.getAvgMaintenanceCost());
            summary.put("预防性维护成本(元)", data.getPreventiveCost());
            summary.put("故障维护成本(元)", data.getCorrectiveCost());
            exportData.add(summary);

            // 简化导出处理
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "维护成本分析报表_" + System.currentTimeMillis() + ".xlsx";
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

            log.info("导出维护成本报表成功，数据行数：{}", exportData.size());

        } catch (Exception e) {
            log.error("导出维护成本报表失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 导出综合报表
     */
    private void exportComprehensiveReport(HttpServletResponse response, MaintenanceReportRequest request) {
        try {
            MaintenanceReportVo.ComprehensiveReportVo data = getComprehensiveReport(request);

            List<Map<String, Object>> exportData = new ArrayList<>();

            // 任务完成率数据
            if (data.getTaskCompletion() != null) {
                Map<String, Object> taskData = new HashMap<>();
                taskData.put("报表类型", "任务完成率");
                taskData.put("总任务数", data.getTaskCompletion().getTotalTasks());
                taskData.put("已完成任务数", data.getTaskCompletion().getCompletedTasks());
                taskData.put("完成率(%)", data.getTaskCompletion().getCompletionRate());
                exportData.add(taskData);
            }

            // 故障响应数据
            if (data.getFaultResponse() != null) {
                Map<String, Object> faultData = new HashMap<>();
                faultData.put("报表类型", "故障响应");
                faultData.put("总故障数", data.getFaultResponse().getTotalFaults());
                faultData.put("已解决故障数", data.getFaultResponse().getResolvedFaults());
                faultData.put("解决率(%)", data.getFaultResponse().getResolutionRate());
                faultData.put("平均响应时间(小时)", data.getFaultResponse().getAvgResponseHours());
                exportData.add(faultData);
            }

            // 维护成本数据
            if (data.getMaintenanceCost() != null) {
                Map<String, Object> costData = new HashMap<>();
                costData.put("报表类型", "维护成本");
                costData.put("总成本(元)", data.getMaintenanceCost().getTotalCost());
                costData.put("人工成本(元)", data.getMaintenanceCost().getLaborCost());
                costData.put("材料成本(元)", data.getMaintenanceCost().getMaterialCost());
                exportData.add(costData);
            }

            // 简化导出处理
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "综合维护报表_" + System.currentTimeMillis() + ".xlsx";
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

            log.info("导出综合报表成功，数据行数：{}", exportData.size());

        } catch (Exception e) {
            log.error("导出综合报表失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getReportConfigOptions() {
        Map<String, Object> options = new HashMap<>();

        // 报表类型选项
        List<Map<String, Object>> reportTypes = new ArrayList<>();
        reportTypes.add(createOption(1, "任务完成率报表"));
        reportTypes.add(createOption(2, "故障响应时间报表"));
        reportTypes.add(createOption(3, "维护成本分析报表"));
        reportTypes.add(createOption(4, "综合报表"));
        options.put("reportTypes", reportTypes);

        // 时间维度选项
        List<Map<String, Object>> timeDimensions = new ArrayList<>();
        timeDimensions.add(createOption(1, "日"));
        timeDimensions.add(createOption(2, "周"));
        timeDimensions.add(createOption(3, "月"));
        timeDimensions.add(createOption(4, "季"));
        timeDimensions.add(createOption(5, "年"));
        options.put("timeDimensions", timeDimensions);

        // 图表类型选项
        List<Map<String, Object>> chartTypes = new ArrayList<>();
        chartTypes.add(createOption("line", "折线图"));
        chartTypes.add(createOption("bar", "柱状图"));
        chartTypes.add(createOption("pie", "饼图"));
        chartTypes.add(createOption("mixed", "混合图"));
        options.put("chartTypes", chartTypes);

        return options;
    }

    @Override
    public boolean saveReportConfig(String configName, Integer reportType, Map<String, Object> config) {
        try {
            // 这里可以实现保存报表配置的逻辑
            log.info("保存报表配置：{}，类型：{}，配置：{}", configName, reportType, config);
            return true;
        } catch (Exception e) {
            log.error("保存报表配置失败", e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getUserReportConfigs(Integer reportType) {
        try {
            // 这里可以实现获取用户报表配置的逻辑
            List<Map<String, Object>> configs = new ArrayList<>();
            // 返回默认配置
            Map<String, Object> defaultConfig = new HashMap<>();
            defaultConfig.put("configId", "default");
            defaultConfig.put("configName", "默认配置");
            defaultConfig.put("reportType", reportType);
            defaultConfig.put("isDefault", true);
            configs.add(defaultConfig);

            return configs;
        } catch (Exception e) {
            log.error("获取用户报表配置失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建选项对象
     */
    private Map<String, Object> createOption(Object value, String label) {
        Map<String, Object> option = new HashMap<>();
        option.put("value", value);
        option.put("label", label);
        return option;
    }
}
